

import {
  createGeneralDetails,
  createPatientDetails,
  createPractitionerDetails,
  createOrganizationDetails,
  createEncounterDetails,
  createBinaryDetails,
  createSignatureDetails
} from './fhir.data.js';
import { fetchImageAsBase64 } from "./common.utils.js";

/**
 * Creates ABDM-compliant ChargeItem resource
 * @param {Object} treatment - Treatment/service details
 * @param {string} patientReference - Patient resource reference
 * @param {string} practitionerReference - Practitioner resource reference
 * @param {string} encounterReference - Encounter resource reference
 * @returns {Object} FHIR ChargeItem resource
 */
export const createABDMChargeItemResource = (
  treatment,
  patientReference,
  practitionerReference,
  encounterReference
) => ({
  resourceType: "ChargeItem",
  id: `charge-${treatment._id || Date.now()}`,
  meta: {
    versionId: "1",
    lastUpdated: new Date().toISOString(),
    profile: ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/ChargeItem"]
  },
  identifier: [
    {
      use: "usual",
      system: "https://www.healtether.com/charge-item",
      value: treatment._id?.toString() || `charge-${Date.now()}`
    }
  ],
  status: "billable",
  code: {
    coding: [
      {
        system: "http://snomed.info/sct",
        code: treatment.snomedCode || "11429006", // Consultation code
        display: treatment.treatment || "Consultation"
      }
    ],
    text: treatment.treatment || "Consultation"
  },
  subject: {
    reference: patientReference,
    display: "Patient"
  },
  context: {
    reference: encounterReference,
    display: "Encounter"
  },
  occurrenceDateTime: new Date().toISOString(),
  performer: [
    {
      function: {
        coding: [
          {
            system: "http://terminology.hl7.org/CodeSystem/v3-ParticipationType",
            code: "PRF",
            display: "Performer"
          }
        ]
      },
      actor: {
        reference: practitionerReference,
        display: "Practitioner"
      }
    }
  ],
  quantity: {
    value: treatment.quantity || 1,
    unit: treatment.unit || "service",
    system: "http://unitsofmeasure.org",
    code: "1"
  },
  priceOverride: {
    value: parseFloat(treatment.amount?.toString() || "0"),
    currency: "INR"
  },
  overrideReason: treatment.discRate > 0 ? "Discount applied" : undefined,
  enterer: {
    reference: practitionerReference,
    display: "Practitioner"
  },
  enteredDate: new Date().toISOString()
});

/**
 * Creates ABDM-compliant Invoice resource following NDHM FHIR R4 standards
 * @param {string} id - Invoice ID
 * @param {string} status - Invoice status
 * @param {string} date - Invoice date
 * @param {Object} totalNet - Net amount AFTER taxes (ABDM standard)
 * @param {Object} totalGross - Gross amount BEFORE taxes (ABDM standard)
 * @param {Object} invoice - Invoice data
 * @param {string} patientReference - Patient resource reference
 * @param {string} practitionerReference - Practitioner resource reference
 * @param {string} organizationReference - Organization resource reference
 * @param {string} encounterReference - Encounter resource reference
 * @returns {Object} FHIR Invoice resource compliant with ABDM standards
 */
export const createABDMInvoiceResource = (
  id,
  status,
  date,
  totalNet,
  totalGross,
  invoice,
  patientReference,
  practitionerReference,
  organizationReference,
  encounterReference
) => {
  // Calculate totals according to ABDM standards
  const baseAmount = invoice.treatments?.reduce((sum, treatment) =>
    sum + parseFloat(treatment.amount?.toString() || "0"), 0) || 0;
  const totalDiscount = invoice.treatments?.reduce((sum, treatment) =>
    sum + parseFloat(((treatment.amount * (treatment.discRate || 0)) / 100).toString()), 0) || 0;
  const totalCGST = invoice.treatments?.reduce((sum, treatment) =>
    sum + parseFloat(treatment.cgstAmount?.toString() || "0"), 0) || 0;
  const totalSGST = invoice.treatments?.reduce((sum, treatment) =>
    sum + parseFloat(treatment.sgstAmount?.toString() || "0"), 0) || 0;

  // ABDM Standard: totalGross = amount before taxes, totalNet = amount after taxes
  const calculatedTotalGross = baseAmount - totalDiscount;
  const calculatedTotalNet = calculatedTotalGross + totalCGST + totalSGST;

  return {
    resourceType: "Invoice",
    id: id || "invoice-" + Date.now(),
    meta: {
      versionId: "1",
      lastUpdated: new Date().toISOString(),
      profile: ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Invoice"]
    },
    identifier: [
      {
        value: invoice.invoiceNumber?.toString() || id
      }
    ],
    status: status || "issued",
    type: {
      coding: [
        {
          system: "https://nrces.in/ndhm/fhir/r4/CodeSystem/ndhm-billing-codes",
          code: "00", // Consultation billing code per ABDM standards
          display: "Consultation"
        }
      ]
    },
    subject: {
      reference: patientReference
    },
    date: date || new Date().toISOString(),
    participant: [
      {
        actor: {
          reference: practitionerReference
        }
      }
    ],
    lineItem: invoice.treatments?.map((treatment, index) => ({
      sequence: index + 1,
      chargeItemReference: {
        reference: `ChargeItem/charge-${treatment._id || index}`
      },
      priceComponent: [
        // 1. MRP (informational) - ABDM code "00"
        {
          type: "informational",
          code: {
            coding: [
              {
                system: "https://nrces.in/ndhm/fhir/r4/CodeSystem/ndhm-price-components",
                code: "00",
                display: "MRP"
              }
            ]
          },
          amount: {
            value: parseFloat(treatment.mrp?.toString() || treatment.amount?.toString() || "0"),
            currency: "INR"
          }
        },
        // 2. Rate (base price) - ABDM code "01"
        {
          type: "base",
          code: {
            coding: [
              {
                system: "https://nrces.in/ndhm/fhir/r4/CodeSystem/ndhm-price-components",
                code: "01",
                display: "Rate"
              }
            ]
          },
          amount: {
            value: parseFloat(treatment.amount?.toString() || "0"),
            currency: "INR"
          }
        },
        // 3. Discount - ABDM code "02"
        {
          type: "discount",
          code: {
            coding: [
              {
                system: "https://nrces.in/ndhm/fhir/r4/CodeSystem/ndhm-price-components",
                code: "02",
                display: "Discount"
              }
            ]
          },
          amount: {
            value: parseFloat(((treatment.amount * (treatment.discRate || 0)) / 100).toString()),
            currency: "INR"
          }
        },
        // 4. CGST (tax) - ABDM code "03"
        {
          type: "tax",
          code: {
            coding: [
              {
                system: "https://nrces.in/ndhm/fhir/r4/CodeSystem/ndhm-price-components",
                code: "03",
                display: "CGST"
              }
            ]
          },
          amount: {
            value: parseFloat(treatment.cgstAmount?.toString() || "0"),
            currency: "INR"
          }
        },
        // 5. SGST (tax) - ABDM code "04"
        {
          type: "tax",
          code: {
            coding: [
              {
                system: "https://nrces.in/ndhm/fhir/r4/CodeSystem/ndhm-price-components",
                code: "04",
                display: "SGST"
              }
            ]
          },
          amount: {
            value: parseFloat(treatment.sgstAmount?.toString() || "0"),
            currency: "INR"
          }
        }
      ]
    })) || [],
    // ABDM Standard: totalGross = before taxes, totalNet = after taxes
    totalNet: {
      value: calculatedTotalNet,
      currency: "INR"
    },
    totalGross: {
      value: calculatedTotalGross,
      currency: "INR"
    }
  };
};

/**
 * Creates ABDM-compliant Composition resource for InvoiceRecord
 * @param {string} patientReference - Patient resource reference
 * @param {string} practitionerReference - Practitioner resource reference
 * @param {string} organizationReference - Organization resource reference
 * @param {string} encounterReference - Encounter resource reference
 * @param {string} invoiceReference - Invoice resource reference
 * @param {Array} chargeItemReferences - Array of ChargeItem resource references
 * @param {Object} binaryResource - Binary resource for PDF attachment
 * @returns {Object} FHIR Composition resource
 */
export const createABDMCompositionResource = (
  patientReference,
  practitionerReference,
  organizationReference,
  encounterReference,
  invoiceReference,
  chargeItemReferences,
  binaryResource
) => ({
  resourceType: "Composition",
  id: `composition-invoice-${Date.now()}`,
  meta: {
    versionId: "1",
    lastUpdated: new Date().toISOString(),
    profile: ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/InvoiceRecord"]
  },
  identifier: {
    system: "https://www.healtether.com/invoice-composition",
    value: `invoice-comp-${Date.now()}`
  },
  status: "final",
  type: {
    text: "Invoice Record"
  },
  category: [
    {
      coding: [
        {
          system: "https://nrces.in/ndhm/fhir/r4/CodeSystem/ndhm-record-type",
          code: "invoice",
          display: "Invoice"
        }
      ]
    }
  ],
  subject: {
    reference: patientReference,
    display: "Patient"
  },
  encounter: {
    reference: encounterReference,
    display: "Encounter"
  },
  date: new Date().toISOString(),
  author: [
    {
      reference: practitionerReference,
      display: "Practitioner"
    }
  ],
  title: "Invoice Record",
  custodian: {
    reference: organizationReference,
    display: "Organization"
  },
  section: [
    {
      title: "Invoice Details",
      entry: [
        {
          reference: invoiceReference,
          type: "Invoice"
        },
        ...chargeItemReferences.map(ref => ({
          reference: ref,
          type: "ChargeItem"
        })),
        {
          reference: `Binary/${binaryResource.id}`,
          type: "Binary"
        }
      ]
    }
  ]
});

/**
 * Creates ABDM-compliant DocumentBundle for invoice records
 * @param {Object} compositionResource - FHIR Composition resource
 * @param {Object} patientResource - FHIR Patient resource
 * @param {Object} practitionerResource - FHIR Practitioner resource
 * @param {Object} organizationResource - FHIR Organization resource
 * @param {Object} encounterResource - FHIR Encounter resource
 * @param {Object} invoiceResource - FHIR Invoice resource
 * @param {Array} chargeItemResources - Array of FHIR ChargeItem resources
 * @param {Object} binaryResource - FHIR Binary resource
 * @returns {Object} ABDM-compliant DocumentBundle
 */
export const createABDMInvoiceDocumentBundle = (
  compositionResource,
  patientResource,
  practitionerResource,
  organizationResource,
  encounterResource,
  invoiceResource,
  chargeItemResources,
  binaryResource
) => ({
  resourceType: "Bundle",
  id: `invoice-bundle-${Date.now()}`,
  meta: {
    versionId: "1",
    lastUpdated: new Date().toISOString(),
    profile: ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/DocumentBundle"]
  },
  identifier: {
    system: "https://www.healtether.com/bundle",
    value: `invoice-bundle-${Date.now()}`
  },
  type: "document",
  timestamp: new Date().toISOString(),
  entry: [
    // 1. Composition (must be first entry in document bundle)
    {
      fullUrl: `Composition/${compositionResource.id}`,
      resource: compositionResource
    },
    // 2. Patient
    {
      fullUrl: `Patient/${patientResource.id}`,
      resource: patientResource
    },
    // 3. Practitioner
    {
      fullUrl: `Practitioner/${practitionerResource.id}`,
      resource: practitionerResource
    },
    // 4. Organization
    {
      fullUrl: `Organization/${organizationResource.id}`,
      resource: organizationResource
    },
    // 5. Encounter
    {
      fullUrl: `Encounter/${encounterResource.id}`,
      resource: encounterResource
    },
    // 6. Invoice
    {
      fullUrl: `Invoice/${invoiceResource.id}`,
      resource: invoiceResource
    },
    // 7. ChargeItems
    ...chargeItemResources.map(chargeItem => ({
      fullUrl: `ChargeItem/${chargeItem.id}`,
      resource: chargeItem
    })),
    // 8. Binary (PDF)
    {
      fullUrl: `Binary/${binaryResource.id}`,
      resource: {
        resourceType: "Binary",
        id: binaryResource.id,
        meta: {
          versionId: "1",
          lastUpdated: new Date().toISOString(),
          profile: ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Binary"]
        },
        contentType: binaryResource.contentType,
        data: binaryResource.data
      }
    }
  ]
});

/**
 * Creates ABDM-compliant invoice structure for NDHM FHIR R4 compliance
 * @param {string} artifact - Artifact type (e.g., "InvoiceRecord")
 * @param {Object} clinicData - Clinic/organization data
 * @param {Object} patient - Patient data with ABHA information
 * @param {Object} practitionerData - Practitioner/doctor data
 * @param {Object} invoiceData - Invoice and treatment data
 * @param {Object} appointmentData - Appointment/encounter data
 * @returns {Object} ABDM-compliant invoice structure with DocumentBundle
 */
export const createABDMCompliantInvoiceStructure = async (
  artifact,
  clinicData,
  patient,
  practitionerData,
  invoiceData,
  appointmentData
) => {
  try {
    // Get PDF data
    const base64Data = await fetchImageAsBase64(
      appointmentData.invoiceReport[0].blobName,
      appointmentData?.clinic
    );

    // Create resource references following FHIR standards
    const patientReference = `Patient/${patient._id}`;
    const practitionerReference = `Practitioner/${practitionerData._id}`;
    const organizationReference = `Organization/${clinicData._id}`;
    const encounterReference = `Encounter/${appointmentData._id}`;
    const invoiceReference = `Invoice/invoice-${invoiceData._id}`;

    // Create FHIR resources
    const patientResource = createPatientDetails(
      patient._id,
      patient.abhaNumber,
      patient.abhaAddress,
      {
        text: `${patient.firstName + patient.lastName}`,
        prefix: [`${patient.prefix}`],
      },
      patient.gender,
      patient.birthday,
      patient?.address,
      [`${practitionerData.firstName + practitionerData.lastName}`],
      [],
      patient.mobile
    );

    const practitionerResource = createPractitionerDetails(practitionerData);
    const organizationResource = createOrganizationDetails(clinicData);
    const encounterResource = createEncounterDetails(appointmentData);
    const binaryResource = createBinaryDetails("application/pdf", base64Data);

    // Create ABDM-compliant ChargeItem resources
    const chargeItemResources = invoiceData.treatments?.map(treatment =>
      createABDMChargeItemResource(
        treatment,
        patientReference,
        practitionerReference,
        encounterReference
      )
    ) || [];

    const chargeItemReferences = chargeItemResources.map(item => `ChargeItem/${item.id}`);

    // Create ABDM-compliant Invoice resource
    const invoiceResource = createABDMInvoiceResource(
      `invoice-${invoiceData._id}`,
      "issued",
      new Date().toISOString().split("T")[0],
      null, // Will be calculated internally
      null, // Will be calculated internally
      invoiceData,
      patientReference,
      practitionerReference,
      organizationReference,
      encounterReference
    );

    // Create ABDM-compliant Composition resource
    const compositionResource = createABDMCompositionResource(
      patientReference,
      practitionerReference,
      organizationReference,
      encounterReference,
      invoiceReference,
      chargeItemReferences,
      binaryResource
    );

    // Create ABDM DocumentBundle
    const documentBundle = createABDMInvoiceDocumentBundle(
      compositionResource,
      patientResource,
      practitionerResource,
      organizationResource,
      encounterResource,
      invoiceResource,
      chargeItemResources,
      binaryResource
    );

    // Return ABDM-compliant structure for Communications service
    return {
      // ABDM DocumentBundle (primary structure)
      bundle: documentBundle,

      // Legacy structure for backward compatibility
      general: createGeneralDetails(
        artifact,
        "https://www.healtether.com",
        ["hip1", "hip2"],
        "final",
        "SBX_003515"
      ),
      patient: patientResource,
      practitioners: [practitionerResource],
      organization: organizationResource,
      encounter: encounterResource,
      composition: compositionResource,
      invoice: invoiceResource,
      chargeItems: chargeItemResources,
      binary: binaryResource,
      signature: createSignatureDetails("", [
        `${practitionerData.firstName + practitionerData.lastName}`,
      ]),
    };
  } catch (error) {
    console.error("Error creating ABDM-compliant invoice structure:", error);
    throw new Error(`ABDM Invoice Creation Failed: ${error.message}`);
  }
};
